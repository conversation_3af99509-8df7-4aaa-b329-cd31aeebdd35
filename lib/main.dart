import 'package:amplify_api/amplify_api.dart';
import 'package:amplify_auth_cognito/amplify_auth_cognito.dart';
import 'package:amplify_flutter/amplify_flutter.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:hive/hive.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:monova_ai_stylist/amplify_outputs.dart';
import 'package:monova_ai_stylist/models/ModelProvider.dart';
import 'package:monova_ai_stylist/pages/splashScreen/splashScreen.dart';
import 'package:monova_ai_stylist/services/screenshotService.dart';
import 'package:monova_ai_stylist/services/sharedPrefsService.dart';
import 'package:path_provider/path_provider.dart';

Future<void> main() async {
  try {
    WidgetsFlutterBinding.ensureInitialized();

    if (kIsWeb) {
      await Hive.initFlutter(); // No path needed on web
    } else {
      final appDocumentDirectory = await getApplicationDocumentsDirectory();
      await Hive.initFlutter(appDocumentDirectory.path);
    }

    await _configureAmplify();

    final isLoggedIn = await SharedPrefsService.isLoggedIn();
    runApp(MyApp(isLoggedIn: isLoggedIn));
  } on AmplifyException catch (e) {
    runApp(Text("Error configuring Amplify: ${e.message}"));
  }
}

Future<void> _configureAmplify() async {
  try {
    await Amplify.addPlugins(
      [
        AmplifyAuthCognito(),
        AmplifyAPI(
          options: APIPluginOptions(
            modelProvider: ModelProvider.instance,
          ),
        ),
      ],
    );
    await Amplify.configure(amplifyConfig);
    safePrint('Successfully configured');
  } on Exception catch (e) {
    safePrint('Error configuring Amplify: $e');
  }
}

class MyApp extends StatelessWidget {
  final bool isLoggedIn;
  
  const MyApp({super.key, required this.isLoggedIn});

  @override
  Widget build(BuildContext context) {
    return Center( // Added Center widget
      child: SizedBox( // Added SizedBox widget
        width: 428.0, // Max width for the app content
        child: MaterialApp(
          debugShowCheckedModeBanner: false,
          builder: (context, child) {
        // Wrap the entire app with RepaintBoundary for screenshots
        return RepaintBoundary(
          key: ScreenshotService().screenshotKey,
          child: child!,
        );
      },
      home: const SplashScreen(),
        ),
      ),
    );
  }
}