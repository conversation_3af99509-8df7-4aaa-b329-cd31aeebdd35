<!DOCTYPE html>
<html>
<head>
  <!--
    If you are serving your web app in a path other than the root, change the
    href value below to reflect the base path you are serving from.

    The path provided below has to start and end with a slash "/" in order for
    it to work correctly.

    For more details:
    * https://developer.mozilla.org/en-US/docs/Web/HTML/Element/base

    This is a placeholder for base href that will be replaced by the value of
    the `--base-href` argument provided to `flutter build`.
  -->
  <base href="/">

  <meta charset="UTF-8">
  <meta content="IE=Edge" http-equiv="X-UA-Compatible">
  <meta name="description" content="A new Flutter project.">

  <!-- iOS meta tags & icons -->
  <meta name="apple-mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-status-bar-style" content="black">
  <meta name="apple-mobile-web-app-title" content="flutter_application_1">
  <link rel="apple-touch-icon" href="icons/Icon-192.png">
  <link rel="stylesheet" href="https://fonts.googleapis.com/icon?family=Material+Icons">


  <!-- Favicon -->
  <link rel="icon" type="image/png" href="favicon.png"/>

  <title>flutter_application_1</title>
  <link rel="manifest" href="manifest.json">

  <style>
  @font-face {
    font-family: 'SatoshiR';
    src: url('assets/fonts/Satoshi-Regular.otf') format('opentype');
    font-weight: 400;
  }
  @font-face {
    font-family: 'SatoshiM';
    src: url('assets/fonts/Satoshi-Medium.otf') format('opentype');
    font-weight: 500;
  }
  @font-face {
    font-family: 'SatoshiB';
    src: url('assets/fonts/Satoshi-Bold.otf') format('opentype');
    font-weight: 700;
  }
  @font-face {
    font-family: 'DegularR';
    src: url('assets/fonts/DegularDisplay-Regular.otf') format('opentype');
    font-weight: 400;
  }
  @font-face {
    font-family: 'DegularM';
    src: url('assets/fonts/DegularDisplay-Medium.otf') format('opentype');
    font-weight: 500;
  }

body {
  display: flex;
  justify-content: center;
  align-items: flex-start; /* Align to top, common for mobile UIs */
  min-height: 100vh;
  margin: 0;
  background-color: #f0f0f0; /* Light grey background for contrast */
  overflow-y: auto; /* Ensure scrolling is possible if content exceeds viewport */
}

/* This will be the Flutter app container */
#flutter_app_container {
  width: 100%; /* Start with full width */
  max-width: 428px; /* Max width for mobile view (e.g., iPhone 12/13 Pro Max) */
  /* min-height: 100vh; /* Ensure it takes at least full viewport height */
  box-shadow: 0 0 10px rgba(0,0,0,0.1); /* Optional: subtle shadow for depth */
  background-color: white; /* Default background for the app area */
  overflow: hidden; /* Prevents content from spilling out, especially with fixed width */
}

/* Ensure the Flutter view itself doesn't override this */
flutter-view {
  width: 100% !important;
  height: 100% !important;
}
</style>
</head>


<body>
  <div id="flutter_app_container">
    <script src="flutter_bootstrap.js" async></script>
    <span class="material-icons">home</span> <!-- This span seems like a placeholder or test, will keep it inside for now -->
  </div>
</body>
</html>
